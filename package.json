{"name": "nextjs-da<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "15.3.2", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify-json/solar": "^1.2.2", "@iconify/tailwind4": "^1.0.6", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "daisyui": "^5.0.40", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}